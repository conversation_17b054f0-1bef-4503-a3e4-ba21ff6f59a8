import {
  create<PERSON><PERSON>,
  IChartApi,
  CandlestickSeries,
  LineSeries,
} from "lightweight-charts";
import {
  getChartConfig,
  getSeriesConfig,
  DEFAULT_THEME,
  type ChartTheme,
} from "../config/chartConfig";
import { SMMA_PERIODS } from "../constants/indicators";
import { dataTransformers } from "./dataTransformers";
import type { CryptoCurrencyStatisticsDto } from "../generated";

/**
 * Chart creation and configuration helpers
 */
export const chartHelpers = {
  /**
   * Create a new chart instance with theme-aware configuration
   */
  createChart: (
    container: HTMLDivElement,
    theme: ChartTheme = DEFAULT_THEME,
  ): IChartApi => {
    const containerWidth = container.clientWidth || container.offsetWidth;
    const containerHeight = container.clientHeight || container.offsetHeight;
    const chartConfig = getChartConfig(theme);
    const fallbackWidth = Math.max(
      chartConfig.dimensions.width,
      window.innerWidth * 0.9,
    );
    const fallbackHeight = Math.max(
      chartConfig.dimensions.height,
      window.innerHeight * 0.75 - 150,
    ); // Account for header, legend, padding

    return createChart(container, {
      ...chartConfig,
      width: containerWidth || fallbackWidth,
      height: containerHeight || fallbackHeight,
    });
  },

  /**
   * Setup all chart series with data using theme-aware configuration
   */
  setupChartSeries: (
    chart: IChartApi,
    data: CryptoCurrencyStatisticsDto,
    theme: ChartTheme = DEFAULT_THEME,
  ): void => {
    const seriesConfig = getSeriesConfig(theme);

    // Add candlestick series
    const candlestickSeries = chart.addSeries(
      CandlestickSeries,
      seriesConfig.candlestick,
    );
    const candlestickData = dataTransformers.transformCandlestickData(
      data.indicatorValues,
    );

    if (candlestickData.length > 0) {
      candlestickSeries.setData(candlestickData);
    }

    // Add SMMA series for each period
    SMMA_PERIODS.forEach((period) => {
      const smmaConfig = seriesConfig.smma[period];
      if (smmaConfig) {
        const lineSeries = chart.addSeries(LineSeries, {
          ...smmaConfig,
        });

        const smmaData = dataTransformers.transformSMMAData(
          data.indicatorValues,
          period,
        );
        if (smmaData.length > 0) {
          lineSeries.setData(smmaData);
        }
      }
    });

    // Fit content to show all data
    chart.timeScale().fitContent();
  },

  /**
   * Handle chart resize
   */
  handleResize: (
    chart: IChartApi,
    container: HTMLDivElement,
    theme: ChartTheme = DEFAULT_THEME,
  ): void => {
    const containerWidth = container.clientWidth || container.offsetWidth;
    const containerHeight = container.clientHeight || container.offsetHeight;
    const chartConfig = getChartConfig(theme);
    const fallbackWidth = Math.max(
      chartConfig.dimensions.width,
      window.innerWidth * 0.9,
    );
    const fallbackHeight = Math.max(
      chartConfig.dimensions.height,
      window.innerHeight * 0.75 - 150,
    );

    chart.applyOptions({
      width: containerWidth || fallbackWidth,
      height: containerHeight || fallbackHeight,
    });
  },

  /**
   * Fast zoom functionality for better user experience
   */
  fastZoom: (
    chart: IChartApi,
    zoomIn: boolean,
    theme: ChartTheme = DEFAULT_THEME,
  ): void => {
    const chartConfig = getChartConfig(theme);
    const zoomSpeed = chartConfig.zoom.speed;
    const currentRange = chart.timeScale().getVisibleLogicalRange();
    if (currentRange) {
      const bars = currentRange.to - currentRange.from;
      const direction = zoomIn ? -1 : 1;
      const newRangeBars = bars * zoomSpeed * direction + bars;

      // Keep the center point stable during zoom
      const center = (currentRange.from + currentRange.to) / 2;
      const halfRange = newRangeBars / 2;

      chart.timeScale().setVisibleLogicalRange({
        from: center - halfRange,
        to: center + halfRange,
      });
    }
  },

  /**
   * Clean up chart resources
   */
  cleanup: (chart: IChartApi): void => {
    chart.remove();
  },
};
