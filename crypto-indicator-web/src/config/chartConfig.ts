import { ColorType } from "lightweight-charts";

export type ChartTheme = "light" | "dark";

export const CHART_THEMES = {
  light: {
    layout: {
      background: { type: ColorType.Solid, color: "#ffffff" },
      textColor: "#333",
    },
    grid: {
      vertLines: { color: "#e1e1e1" },
      horzLines: { color: "#e1e1e1" },
    },
    rightPriceScale: {
      borderColor: "#cccccc",
    },
    crosshair: {
      vertLine: {
        color: "#758696",
        labelBackgroundColor: "#4c525e",
      },
      horzLine: {
        color: "#758696",
        labelBackgroundColor: "#4c525e",
      },
    },
  },
  dark: {
    layout: {
      background: { type: ColorType.Solid, color: "#181825" },
      textColor: "#cdd6f4",
    },
    grid: {
      vertLines: { color: "#313244" },
      horzLines: { color: "#313244" },
    },
    rightPriceScale: {
      borderColor: "#45475a",
    },
    crosshair: {
      vertLine: {
        color: "#94e2d5",
        labelBackgroundColor: "#45475a",
      },
      horzLine: {
        color: "#94e2d5",
        labelBackgroundColor: "#45475a",
      },
    },
  },
} as const;

export const DEFAULT_THEME: ChartTheme = "dark";

export const CHART_CONFIG = {
  dimensions: {
    width: 1200,
    height: 600,
  },
  timeScale: {
    timeVisible: true,
    secondsVisible: false,
  },
  crosshair: {
    mode: 1,
  },
  zoom: {
    speed: 1 / 30, // Faster zoom speed (1/3 instead of default 1/8)
  },
} as const;

export const SERIES_THEMES = {
  light: {
    candlestick: {
      upColor: "#26a69a",
      downColor: "#ef5350",
      borderVisible: false,
      wickUpColor: "#26a69a",
      wickDownColor: "#ef5350",
    },
    smma: {
      15: { color: "#FF6B35", lineWidth: 1 },
      29: { color: "#2196F3", lineWidth: 2 },
    },
  },
  dark: {
    candlestick: {
      upColor: "#a6e3a1",
      downColor: "#f38ba8",
      borderVisible: false,
      wickUpColor: "#a6e3a1",
      wickDownColor: "#f38ba8",
    },
    smma: {
      15: { color: "#e7ff00", lineWidth: 2 },
      29: { color: "#0033ff", lineWidth: 2 },
    },
  },
} as const;

// Helper functions to get theme-specific configurations
export const getChartConfig = (theme: ChartTheme = DEFAULT_THEME) => ({
  ...CHART_CONFIG,
  ...CHART_THEMES[theme],
});

export const getSeriesConfig = (theme: ChartTheme = DEFAULT_THEME) =>
  SERIES_THEMES[theme];
