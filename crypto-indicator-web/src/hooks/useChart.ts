import { useEffect, useRef } from "react";
import type { IChart<PERSON><PERSON> } from "lightweight-charts";
import { chartHelpers } from "../utils/chartHelpers";
import { DEFAULT_THEME, type ChartTheme } from "../config/chartConfig";
import type { CryptoCurrencyStatisticsDto } from "../generated";

interface UseChartReturn {
  chartRef: React.RefObject<IChartApi | null>;
  containerRef: React.RefObject<HTMLDivElement | null>;
}

/**
 * Hook for managing chart lifecycle and interactions
 * Handles chart creation, data setup, and cleanup
 */
export const useChart = (data: CryptoCurrencyStatisticsDto, theme: ChartTheme = DEFAULT_THEME): UseChartReturn => {
  const chartRef = useRef<IChartApi | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Small delay to ensure container dimensions are calculated
    const timeoutId = setTimeout(() => {
      if (!containerRef.current) return;

      // Create chart instance with theme
      const chart = chartHelpers.createChart(containerRef.current, theme);
      chartRef.current = chart;

      // Setup series with data and theme
      chartHelpers.setupChartSeries(chart, data, theme);
    }, 10);

    // Handle window resize
    const handleResize = () => {
      if (containerRef.current && chartRef.current) {
        chartHelpers.handleResize(chartRef.current, containerRef.current, theme);
      }
    };

    // Handle fast zoom with mouse wheel
    const handleWheel = (event: WheelEvent) => {
      if (chartRef.current) {
        event.preventDefault();
        const zoomIn = event.deltaY < 0;
        chartHelpers.fastZoom(chartRef.current, zoomIn, theme);
      }
    };

    window.addEventListener("resize", handleResize);

    if (containerRef.current) {
      containerRef.current.addEventListener("wheel", handleWheel, { passive: false });
    }

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener("resize", handleResize);
      if (containerRef.current) {
        containerRef.current.removeEventListener("wheel", handleWheel);
      }
      if (chartRef.current) {
        chartHelpers.cleanup(chartRef.current);
        chartRef.current = null;
      }
    };
  }, [data, theme]);

  return {
    chartRef,
    containerRef,
  };
};
