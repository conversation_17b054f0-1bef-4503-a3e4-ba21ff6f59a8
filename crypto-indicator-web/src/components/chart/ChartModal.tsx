import React from "react";
import { ChartContainer } from "./ChartContainer";
import { ChartHeader } from "./ChartHeader";
import { DEFAULT_THEME, type ChartTheme } from "../../config/chartConfig";
import type { ChartModalProps } from "../../types/chart";

interface ChartModalPropsWithTheme extends ChartModalProps {
  theme?: ChartTheme;
}

export const ChartModal: React.FC<ChartModalPropsWithTheme> = ({
  data,
  onClose,
  theme = DEFAULT_THEME,
}) => {
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [onClose]);

  const themeClass = theme === "dark" ? "dark-theme" : "";

  return (
    <div className="chart-modal" onClick={handleBackdropClick}>
      <div className={`chart-modal-content ${themeClass}`}>
        <ChartHeader
          symbol={data.symbol}
          conversionCurrency={data.conversionCurrency}
          onClose={onClose}
        />
        <ChartContainer data={data} theme={theme} />
      </div>
    </div>
  );
};
